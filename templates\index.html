{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-3">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم الرئيسية
            </h1>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                إجمالي الأدوية
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ total_medicines }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-pills fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                الفئات العلاجية
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ total_categories }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                مخزون منخفض
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ low_stock }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                منتهية الصلاحية قريباً
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ expiring_soon }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('add_medicine') }}" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                إضافة دواء جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('medicines') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-search me-2"></i>
                                البحث في الأدوية
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('inventory') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-boxes me-2"></i>
                                إدارة المخزون
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-chart-bar me-2"></i>
                                عرض التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التنبيهات المهمة -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تنبيهات المخزون المنخفض
                    </h6>
                </div>
                <div class="card-body">
                    {% if low_stock > 0 %}
                        <p class="text-warning">
                            يوجد {{ low_stock }} دواء بمخزون منخفض يحتاج إلى إعادة تموين
                        </p>
                        <a href="{{ url_for('alerts') }}" class="btn btn-warning btn-sm">
                            عرض التفاصيل
                        </a>
                    {% else %}
                        <p class="text-success">
                            <i class="fas fa-check-circle me-2"></i>
                            جميع الأدوية متوفرة بكميات كافية
                        </p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-calendar-times me-2"></i>
                        تنبيهات انتهاء الصلاحية
                    </h6>
                </div>
                <div class="card-body">
                    {% if expiring_soon > 0 %}
                        <p class="text-danger">
                            يوجد {{ expiring_soon }} دواء ستنتهي صلاحيته خلال 30 يوم
                        </p>
                        <a href="{{ url_for('alerts') }}" class="btn btn-danger btn-sm">
                            عرض التفاصيل
                        </a>
                    {% else %}
                        <p class="text-success">
                            <i class="fas fa-check-circle me-2"></i>
                            لا توجد أدوية قريبة من انتهاء الصلاحية
                        </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تحديث الإحصائيات كل 5 دقائق
    setInterval(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}
