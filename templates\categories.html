{% extends "base.html" %}

{% block title %}الفئات العلاجية - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-3">
                <i class="fas fa-tags me-2"></i>
                الفئات العلاجية
            </h1>
        </div>
        <div class="col-md-6 text-end">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus me-2"></i>
                إضافة فئة جديدة
            </button>
        </div>
    </div>

    <!-- جدول الفئات -->
    <div class="card">
        <div class="card-body">
            {% if categories %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الفئة</th>
                                <th>الوصف</th>
                                <th>عدد الأدوية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category in categories %}
                                <tr>
                                    <td>
                                        <strong>{{ category.name }}</strong>
                                    </td>
                                    <td>{{ category.description or '-' }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ category.medicines|length }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                    onclick="editCategory({{ category.id }}, '{{ category.name }}', '{{ category.description or '' }}'))"
                                                    title="تعديل الفئة">
                                                <i class="fas fa-edit" aria-hidden="true"></i>
                                                <span class="visually-hidden">تعديل</span>
                                            </button>
                                            <a href="{{ url_for('medicines', category=category.id) }}"
                                               class="btn btn-sm btn-outline-info"
                                               title="عرض الأدوية في هذه الفئة">
                                                <i class="fas fa-eye" aria-hidden="true"></i>
                                                <span class="visually-hidden">عرض الأدوية</span>
                                            </a>
                                            {% if category.medicines|length == 0 %}
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteCategory({{ category.id }}, '{{ category.name }}'))"
                                                        title="حذف الفئة">
                                                    <i class="fas fa-trash" aria-hidden="true"></i>
                                                    <span class="visually-hidden">حذف</span>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد فئات علاجية</h5>
                    <p class="text-muted">ابدأ بإضافة الفئات العلاجية لتنظيم الأدوية</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول فئة
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نافذة إضافة فئة جديدة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة فئة علاجية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addCategoryForm">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">اسم الفئة</label>
                        <input type="text" class="form-control" id="categoryName" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addCategory()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل فئة -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الفئة العلاجية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="editCategoryForm">
                    <div class="mb-3">
                        <label for="editCategoryName" class="form-label">اسم الفئة</label>
                        <input type="text" class="form-control" id="editCategoryName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editCategoryDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="editCategoryDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="updateCategory()">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الفئة العلاجية <strong id="categoryToDelete"></strong>؟</p>
                <p class="text-warning">يمكن حذف الفئات التي لا تحتوي على أدوية فقط.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteCategory()">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentCategoryId = null;

function addCategory() {
    const name = document.getElementById('categoryName').value.trim();
    const description = document.getElementById('categoryDescription').value.trim();
    
    if (!name) {
        alert('يرجى إدخال اسم الفئة');
        return;
    }
    
    // هنا يمكن إضافة AJAX لإضافة الفئة
    alert('سيتم تنفيذ هذه الميزة قريباً - إضافة فئة: ' + name);
    bootstrap.Modal.getInstance(document.getElementById('addCategoryModal')).hide();
    
    // إعادة تعيين النموذج
    document.getElementById('addCategoryForm').reset();
}

function editCategory(id, name, description) {
    currentCategoryId = id;
    document.getElementById('editCategoryName').value = name;
    document.getElementById('editCategoryDescription').value = description;
    new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
}

function updateCategory() {
    const name = document.getElementById('editCategoryName').value.trim();
    const description = document.getElementById('editCategoryDescription').value.trim();
    
    if (!name) {
        alert('يرجى إدخال اسم الفئة');
        return;
    }
    
    // هنا يمكن إضافة AJAX لتحديث الفئة
    alert('سيتم تنفيذ هذه الميزة قريباً - تحديث فئة رقم: ' + currentCategoryId);
    bootstrap.Modal.getInstance(document.getElementById('editCategoryModal')).hide();
}

function deleteCategory(id, name) {
    currentCategoryId = id;
    document.getElementById('categoryToDelete').textContent = name;
    new bootstrap.Modal(document.getElementById('deleteCategoryModal')).show();
}

function confirmDeleteCategory() {
    // هنا يمكن إضافة AJAX لحذف الفئة
    alert('سيتم تنفيذ هذه الميزة قريباً - حذف فئة رقم: ' + currentCategoryId);
    bootstrap.Modal.getInstance(document.getElementById('deleteCategoryModal')).hide();
}
</script>
{% endblock %}
