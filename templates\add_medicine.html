{% extends "base.html" %}

{% block title %}إضافة دواء جديد - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- العنوان -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-plus me-2"></i>
                    إضافة دواء جديد
                </h1>
                <a href="{{ url_for('medicines') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>

            <!-- نموذج إضافة الدواء -->
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- اسم الدواء -->
                            <div class="col-md-6 mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control") }}
                                {% if form.name.errors %}
                                    <div class="text-danger">
                                        {% for error in form.name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- الاسم العلمي -->
                            <div class="col-md-6 mb-3">
                                {{ form.generic_name.label(class="form-label") }}
                                {{ form.generic_name(class="form-control") }}
                            </div>
                        </div>

                        <div class="row">
                            <!-- القوة/التركيز -->
                            <div class="col-md-6 mb-3">
                                {{ form.strength.label(class="form-label") }}
                                {{ form.strength(class="form-control", placeholder="مثال: 500mg") }}
                            </div>

                            <!-- الباركود -->
                            <div class="col-md-6 mb-3">
                                {{ form.barcode.label(class="form-label") }}
                                {{ form.barcode(class="form-control") }}
                            </div>
                        </div>

                        <div class="row">
                            <!-- الشركة المصنعة -->
                            <div class="col-md-6 mb-3">
                                {{ form.manufacturer.label(class="form-label") }}
                                {{ form.manufacturer(class="form-control") }}
                            </div>

                            <!-- المورد -->
                            <div class="col-md-6 mb-3">
                                {{ form.supplier_id.label(class="form-label") }}
                                {{ form.supplier_id(class="form-select") }}
                            </div>
                        </div>

                        <div class="row">
                            <!-- الفئة العلاجية -->
                            <div class="col-md-6 mb-3">
                                {{ form.category_id.label(class="form-label") }}
                                {{ form.category_id(class="form-select") }}
                                {% if form.category_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.category_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- المحمل -->
                            <div class="col-md-6 mb-3">
                                {{ form.carrier_id.label(class="form-label") }}
                                {{ form.carrier_id(class="form-select") }}
                                {% if form.carrier_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.carrier_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- التركيب -->
                        <div class="mb-3">
                            {{ form.composition.label(class="form-label") }}
                            {{ form.composition(class="form-control", rows="3", placeholder="اكتب تركيب الدواء والمواد الفعالة...") }}
                        </div>

                        <!-- الوصف -->
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="3", placeholder="معلومات إضافية عن الدواء...") }}
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الدواء
                            </button>
                            <a href="{{ url_for('medicines') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- معلومات مساعدة -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات مساعدة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>الحقول المطلوبة:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>اسم الدواء</li>
                                <li><i class="fas fa-check text-success me-2"></i>الفئة العلاجية</li>
                                <li><i class="fas fa-check text-success me-2"></i>المحمل</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>نصائح:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>استخدم أسماء واضحة ومفهومة</li>
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>تأكد من صحة الباركود</li>
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>اختر الفئة والمحمل المناسبين</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// التحقق من صحة النموذج قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.querySelector('#name').value.trim();
    const category = document.querySelector('#category_id').value;
    const carrier = document.querySelector('#carrier_id').value;
    
    if (!name || !category || !carrier) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
});

// تنسيق حقل الباركود
document.querySelector('#barcode').addEventListener('input', function(e) {
    // إزالة أي أحرف غير رقمية
    this.value = this.value.replace(/[^0-9]/g, '');
});
</script>
{% endblock %}
