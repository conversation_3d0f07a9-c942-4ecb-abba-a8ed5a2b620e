{% extends "base.html" %}

{% block title %}التنبيهات - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                التنبيهات والإشعارات
            </h1>
        </div>
    </div>

    <!-- إحصائيات التنبيهات -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                أدوية بمخزون منخفض
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ low_stock_medicines|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">
                                أدوية منتهية الصلاحية قريباً
                            </div>
                            <div class="h5 mb-0 font-weight-bold">{{ expiring_medicines|length }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تنبيهات المخزون المنخفض -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-exclamation-triangle me-2"></i>
                أدوية بمخزون منخفض
            </h5>
        </div>
        <div class="card-body">
            {% if low_stock_medicines %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الدواء</th>
                                <th>الفئة العلاجية</th>
                                <th>الكمية المتاحة</th>
                                <th>الحد الأدنى</th>
                                <th>حالة التنبيه</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine, inventory in low_stock_medicines %}
                                <tr>
                                    <td>
                                        <strong>{{ medicine.name }}</strong>
                                        {% if medicine.strength %}
                                            <br><small class="text-muted">{{ medicine.strength }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ medicine.category.name }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ inventory.quantity }}</span>
                                    </td>
                                    <td>{{ inventory.min_stock_level }}</td>
                                    <td>
                                        {% if inventory.quantity == 0 %}
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        {% else %}
                                            <span class="badge bg-warning">مخزون منخفض</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('add_inventory', medicine_id=medicine.id) }}" 
                                           class="btn btn-sm btn-success">
                                            <i class="fas fa-plus me-1"></i>
                                            إضافة مخزون
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز!</h5>
                    <p class="text-muted">جميع الأدوية متوفرة بكميات كافية</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- تنبيهات انتهاء الصلاحية -->
    <div class="card">
        <div class="card-header bg-danger text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar-times me-2"></i>
                أدوية منتهية الصلاحية أو قريبة من الانتهاء
            </h5>
        </div>
        <div class="card-body">
            {% if expiring_medicines %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الدواء</th>
                                <th>رقم الدفعة</th>
                                <th>الكمية</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الأيام المتبقية</th>
                                <th>حالة التنبيه</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine, inventory in expiring_medicines %}
                                {% set days_to_expiry = (inventory.expiry_date - today).days %}
                                <tr>
                                    <td>
                                        <strong>{{ medicine.name }}</strong>
                                        {% if medicine.strength %}
                                            <br><small class="text-muted">{{ medicine.strength }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ inventory.batch_number or '-' }}</td>
                                    <td>{{ inventory.quantity }}</td>
                                    <td>{{ inventory.expiry_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if days_to_expiry < 0 %}
                                            <span class="text-danger">{{ days_to_expiry|abs }} يوم منتهي</span>
                                        {% else %}
                                            <span class="text-warning">{{ days_to_expiry }} يوم</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if days_to_expiry < 0 %}
                                            <span class="badge bg-danger">منتهي الصلاحية</span>
                                        {% elif days_to_expiry <= 7 %}
                                            <span class="badge bg-danger">خطر - أسبوع أو أقل</span>
                                        {% elif days_to_expiry <= 30 %}
                                            <span class="badge bg-warning">تحذير - شهر أو أقل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            {% if days_to_expiry < 0 %}
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="markAsExpired({{ inventory.id }})">
                                                    <i class="fas fa-trash me-1"></i>
                                                    إزالة
                                                </button>
                                            {% else %}
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="createDiscount({{ inventory.id }})">
                                                    <i class="fas fa-percentage me-1"></i>
                                                    خصم
                                                </button>
                                            {% endif %}
                                            <a href="{{ url_for('edit_medicine', id=medicine.id) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز!</h5>
                    <p class="text-muted">لا توجد أدوية قريبة من انتهاء الصلاحية</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نافذة تأكيد إزالة الأدوية المنتهية الصلاحية -->
<div class="modal fade" id="expiredModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إزالة الدواء المنتهي الصلاحية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إزالة هذا الدواء من المخزون؟</p>
                <p class="text-danger">سيتم تسجيل هذا الإجراء في سجل الأدوية المنتهية الصلاحية.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="confirmExpiredRemoval()">تأكيد الإزالة</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إنشاء خصم -->
<div class="modal fade" id="discountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء خصم للدواء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="discountForm">
                    <div class="mb-3">
                        <label for="discountPercentage" class="form-label">نسبة الخصم (%)</label>
                        <input type="number" class="form-control" id="discountPercentage" min="1" max="50" value="10">
                    </div>
                    <div class="mb-3">
                        <label for="discountReason" class="form-label">سبب الخصم</label>
                        <select class="form-select" id="discountReason">
                            <option value="قريب من انتهاء الصلاحية">قريب من انتهاء الصلاحية</option>
                            <option value="تصريف مخزون">تصريف مخزون</option>
                            <option value="عرض خاص">عرض خاص</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="applyDiscount()">تطبيق الخصم</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentInventoryId = null;

function markAsExpired(inventoryId) {
    currentInventoryId = inventoryId;
    new bootstrap.Modal(document.getElementById('expiredModal')).show();
}

function createDiscount(inventoryId) {
    currentInventoryId = inventoryId;
    new bootstrap.Modal(document.getElementById('discountModal')).show();
}

function confirmExpiredRemoval() {
    if (currentInventoryId) {
        // هنا يمكن إضافة AJAX لإزالة الدواء المنتهي الصلاحية
        alert('سيتم تنفيذ هذه الميزة قريباً - إزالة الدواء رقم: ' + currentInventoryId);
        bootstrap.Modal.getInstance(document.getElementById('expiredModal')).hide();
    }
}

function applyDiscount() {
    if (currentInventoryId) {
        const percentage = document.getElementById('discountPercentage').value;
        const reason = document.getElementById('discountReason').value;
        
        // هنا يمكن إضافة AJAX لتطبيق الخصم
        alert(`سيتم تنفيذ هذه الميزة قريباً - خصم ${percentage}% للدواء رقم: ${currentInventoryId}`);
        bootstrap.Modal.getInstance(document.getElementById('discountModal')).hide();
    }
}

// تحديث الصفحة كل 5 دقائق للحصول على أحدث التنبيهات
setInterval(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
