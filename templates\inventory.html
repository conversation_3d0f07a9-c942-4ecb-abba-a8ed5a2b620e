{% extends "base.html" %}

{% block title %}إدارة المخزون - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان والبحث -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-3">
                <i class="fas fa-boxes me-2"></i>
                إدارة المخزون
            </h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ url_for('medicines') }}" class="btn btn-outline-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة مخزون لدواء
            </a>
        </div>
    </div>

    <!-- فلتر البحث -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-8">
                    <label for="search" class="form-label">البحث في الأدوية</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search }}" placeholder="ابحث عن اسم الدواء...">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المخزون -->
    <div class="card">
        <div class="card-body">
            {% if inventory_items.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الدواء</th>
                                <th>رقم الدفعة</th>
                                <th>الكمية المتاحة</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine, inventory in inventory_items.items %}
                                <tr>
                                    <td>
                                        <strong>{{ medicine.name }}</strong>
                                        {% if medicine.strength %}
                                            <br><small class="text-muted">{{ medicine.strength }}</small>
                                        {% endif %}
                                        <br><span class="badge bg-primary">{{ medicine.category.name }}</span>
                                    </td>
                                    <td>{{ inventory.batch_number or '-' }}</td>
                                    <td>
                                        <span class="badge {% if inventory.quantity <= inventory.min_stock_level %}bg-danger{% elif inventory.quantity <= inventory.min_stock_level * 2 %}bg-warning{% else %}bg-success{% endif %}">
                                            {{ inventory.quantity }}
                                        </span>
                                        <br><small class="text-muted">الحد الأدنى: {{ inventory.min_stock_level }}</small>
                                    </td>
                                    <td>
                                        {% if inventory.purchase_price %}
                                            {{ "%.2f"|format(inventory.purchase_price) }} ج.م
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if inventory.selling_price %}
                                            {{ "%.2f"|format(inventory.selling_price) }} ج.م
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if inventory.expiry_date %}
                                            {% set days_to_expiry = (inventory.expiry_date - today).days %}
                                            <span class="{% if days_to_expiry < 0 %}text-danger{% elif days_to_expiry <= 30 %}text-warning{% else %}text-success{% endif %}">
                                                {{ inventory.expiry_date.strftime('%Y-%m-%d') }}
                                            </span>
                                            {% if days_to_expiry < 0 %}
                                                <br><small class="text-danger">منتهي الصلاحية</small>
                                            {% elif days_to_expiry <= 30 %}
                                                <br><small class="text-warning">{{ days_to_expiry }} يوم متبقي</small>
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if inventory.quantity <= 0 %}
                                            <span class="badge bg-danger">نفد المخزون</span>
                                        {% elif inventory.quantity <= inventory.min_stock_level %}
                                            <span class="badge bg-warning">مخزون منخفض</span>
                                        {% else %}
                                            <span class="badge bg-success">متوفر</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="editInventory({{ inventory.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="addStock({{ inventory.id }})">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="reduceStock({{ inventory.id }})">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                {% if inventory_items.pages > 1 %}
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if inventory_items.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('inventory', page=inventory_items.prev_num, search=search) }}">السابق</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in inventory_items.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != inventory_items.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('inventory', page=page_num, search=search) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if inventory_items.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('inventory', page=inventory_items.next_num, search=search) }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد مخزون</h5>
                    <p class="text-muted">لم يتم العثور على مخزون يطابق معايير البحث</p>
                    <a href="{{ url_for('medicines') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مخزون جديد
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نافذة تعديل المخزون -->
<div class="modal fade" id="editInventoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editInventoryForm">
                    <div class="mb-3">
                        <label for="editQuantity" class="form-label">الكمية</label>
                        <input type="number" class="form-control" id="editQuantity" min="0">
                    </div>
                    <div class="mb-3">
                        <label for="editMinStock" class="form-label">الحد الأدنى للمخزون</label>
                        <input type="number" class="form-control" id="editMinStock" min="0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveInventoryChanges()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة مخزون -->
<div class="modal fade" id="addStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addStockForm">
                    <div class="mb-3">
                        <label for="addQuantity" class="form-label">الكمية المضافة</label>
                        <input type="number" class="form-control" id="addQuantity" min="1">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="addStockQuantity()">إضافة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentInventoryId = null;

function editInventory(id) {
    currentInventoryId = id;
    new bootstrap.Modal(document.getElementById('editInventoryModal')).show();
}

function addStock(id) {
    currentInventoryId = id;
    new bootstrap.Modal(document.getElementById('addStockModal')).show();
}

function reduceStock(id) {
    const quantity = prompt('كم الكمية المراد خصمها؟');
    if (quantity && quantity > 0) {
        // هنا يمكن إضافة AJAX لتحديث المخزون
        alert('سيتم تنفيذ هذه الميزة قريباً');
    }
}

function saveInventoryChanges() {
    // هنا يمكن إضافة AJAX لحفظ التغييرات
    alert('سيتم تنفيذ هذه الميزة قريباً');
}

function addStockQuantity() {
    // هنا يمكن إضافة AJAX لإضافة المخزون
    alert('سيتم تنفيذ هذه الميزة قريباً');
}
</script>
{% endblock %}
