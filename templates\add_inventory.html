{% extends "base.html" %}

{% block title %}إضافة مخزون - {{ medicine.name }} - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- العنوان -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مخزون: {{ medicine.name }}
                </h1>
                <a href="{{ url_for('inventory') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمخزون
                </a>
            </div>

            <!-- معلومات الدواء -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الدواء
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم الدواء:</strong> {{ medicine.name }}</p>
                            <p><strong>الاسم العلمي:</strong> {{ medicine.generic_name or '-' }}</p>
                            <p><strong>القوة:</strong> {{ medicine.strength or '-' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الفئة العلاجية:</strong> {{ medicine.category.name }}</p>
                            <p><strong>المحمل:</strong> {{ medicine.carrier.name }}</p>
                            <p><strong>الشركة المصنعة:</strong> {{ medicine.manufacturer or '-' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة المخزون -->
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- رقم الدفعة -->
                            <div class="col-md-6 mb-3">
                                {{ form.batch_number.label(class="form-label") }}
                                {{ form.batch_number(class="form-control", placeholder="مثال: BATCH001") }}
                            </div>

                            <!-- الكمية -->
                            <div class="col-md-6 mb-3">
                                {{ form.quantity.label(class="form-label") }}
                                {{ form.quantity(class="form-control") }}
                                {% if form.quantity.errors %}
                                    <div class="text-danger">
                                        {% for error in form.quantity.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- سعر الشراء -->
                            <div class="col-md-6 mb-3">
                                {{ form.purchase_price.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.purchase_price(class="form-control", step="0.01") }}
                                    <span class="input-group-text">ج.م</span>
                                </div>
                            </div>

                            <!-- سعر البيع -->
                            <div class="col-md-6 mb-3">
                                {{ form.selling_price.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.selling_price(class="form-control", step="0.01") }}
                                    <span class="input-group-text">ج.م</span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تاريخ الانتهاء -->
                            <div class="col-md-6 mb-3">
                                {{ form.expiry_date.label(class="form-label") }}
                                {{ form.expiry_date(class="form-control") }}
                            </div>

                            <!-- الحد الأدنى للمخزون -->
                            <div class="col-md-6 mb-3">
                                {{ form.min_stock_level.label(class="form-label") }}
                                {{ form.min_stock_level(class="form-control") }}
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ المخزون
                            </button>
                            <a href="{{ url_for('inventory') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نصائح مفيدة -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح مفيدة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إدارة المخزون:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>تأكد من صحة تاريخ الانتهاء</li>
                                <li><i class="fas fa-check text-success me-2"></i>احرص على دقة الكميات</li>
                                <li><i class="fas fa-check text-success me-2"></i>استخدم أرقام دفعات واضحة</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>الأسعار:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-info text-info me-2"></i>سعر البيع يجب أن يكون أعلى من سعر الشراء</li>
                                <li><i class="fas fa-info text-info me-2"></i>احسب هامش الربح المناسب</li>
                                <li><i class="fas fa-info text-info me-2"></i>راجع أسعار السوق</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// حساب هامش الربح تلقائياً
document.getElementById('purchase_price').addEventListener('input', calculateProfit);
document.getElementById('selling_price').addEventListener('input', calculateProfit);

function calculateProfit() {
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (purchasePrice > 0 && sellingPrice > 0) {
        const profit = sellingPrice - purchasePrice;
        const profitMargin = ((profit / purchasePrice) * 100).toFixed(2);
        
        // إظهار هامش الربح (يمكن إضافة عنصر HTML لعرضه)
        console.log(`هامش الربح: ${profitMargin}%`);
    }
}

// التحقق من تاريخ الانتهاء
document.getElementById('expiry_date').addEventListener('change', function() {
    const expiryDate = new Date(this.value);
    const today = new Date();
    
    if (expiryDate <= today) {
        alert('تحذير: تاريخ الانتهاء يجب أن يكون في المستقبل');
        this.focus();
    }
});

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const quantity = parseInt(document.getElementById('quantity').value);
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (quantity <= 0) {
        e.preventDefault();
        alert('يجب أن تكون الكمية أكبر من صفر');
        return false;
    }
    
    if (sellingPrice > 0 && purchasePrice > 0 && sellingPrice <= purchasePrice) {
        if (!confirm('سعر البيع أقل من أو يساوي سعر الشراء. هل تريد المتابعة؟')) {
            e.preventDefault();
            return false;
        }
    }
});
</script>
{% endblock %}
