{% extends "base.html" %}

{% block title %}إدارة الأدوية - نظام إدارة الصيدلية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان والبحث -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-3">
                <i class="fas fa-pills me-2"></i>
                إدارة الأدوية
            </h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ url_for('add_medicine') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة دواء جديد
            </a>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search }}" placeholder="ابحث عن اسم الدواء...">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">الفئة العلاجية</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" 
                                    {% if category_filter == category.id|string %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="carrier" class="form-label">المحمل</label>
                    <select class="form-select" id="carrier" name="carrier">
                        <option value="">جميع المحامل</option>
                        {% for carrier in carriers %}
                            <option value="{{ carrier.id }}" 
                                    {% if carrier_filter == carrier.id|string %}selected{% endif %}>
                                {{ carrier.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الأدوية -->
    <div class="card">
        <div class="card-body">
            {% if medicines.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الدواء</th>
                                <th>الاسم العلمي</th>
                                <th>الفئة العلاجية</th>
                                <th>المحمل</th>
                                <th>الشركة المصنعة</th>
                                <th>الباركود</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine in medicines.items %}
                                <tr>
                                    <td>
                                        <strong>{{ medicine.name }}</strong>
                                        {% if medicine.strength %}
                                            <br><small class="text-muted">{{ medicine.strength }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ medicine.generic_name or '-' }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ medicine.category.name }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ medicine.carrier.name }}</span>
                                    </td>
                                    <td>{{ medicine.manufacturer or '-' }}</td>
                                    <td>{{ medicine.barcode or '-' }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_medicine', id=medicine.id) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('add_inventory', medicine_id=medicine.id) }}" 
                                               class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-plus"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteMedicine({{ medicine.id }}, '{{ medicine.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                {% if medicines.pages > 1 %}
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if medicines.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('medicines', page=medicines.prev_num, search=search, category=category_filter, carrier=carrier_filter) }}">السابق</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in medicines.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != medicines.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('medicines', page=page_num, search=search, category=category_filter, carrier=carrier_filter) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if medicines.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('medicines', page=medicines.next_num, search=search, category=category_filter, carrier=carrier_filter) }}">التالي</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أدوية</h5>
                    <p class="text-muted">لم يتم العثور على أدوية تطابق معايير البحث</p>
                    <a href="{{ url_for('add_medicine') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول دواء
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الدواء <strong id="medicineName"></strong>؟</p>
                <p class="text-danger">سيتم حذف جميع بيانات المخزون المرتبطة بهذا الدواء أيضاً.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function deleteMedicine(id, name) {
    document.getElementById('medicineName').textContent = name;
    document.getElementById('deleteForm').action = '/medicines/' + id + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
